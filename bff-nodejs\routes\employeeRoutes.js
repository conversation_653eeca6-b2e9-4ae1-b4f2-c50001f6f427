const express = require('express');
const router = express.Router();
const springBootService = require('../services/springBootService');
const { authenticateToken, userOrAdmin, adminOnly } = require('../middleware/authMiddleware');

// Middleware for request logging
router.use((req, res, next) => {
  console.log(`📝 BFF Request: ${req.method} ${req.originalUrl} by ${req.user?.username || 'anonymous'}`);
  next();
});

// GET /api/employees/search/:query - Search employees (must be before /:id route) - Protected
router.get('/search/:query', authenticateToken(), async (req, res, next) => {
  try {
    const query = req.params.query;
    console.log(`🔍 Searching employees with query: ${query}`);

    // For now, get all data and filter (in production, implement proper search)
    const allData = await springBootService.getCombinedEmployeeData();

    const filteredData = Array.isArray(allData) ? allData.filter(emp =>
      emp.empName?.toLowerCase().includes(query.toLowerCase()) ||
      emp.deptName?.toLowerCase().includes(query.toLowerCase()) ||
      emp.managerName?.toLowerCase().includes(query.toLowerCase()) ||
      emp.employeeId?.toString().includes(query)
    ) : [];

    res.json({
      success: true,
      data: filteredData,
      query: query,
      count: filteredData.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`❌ Error searching employees with query ${req.params.query}:`, error);
    next({
      status: error.response?.status || 500,
      message: error.response?.data?.message || error.message || 'Failed to search employees'
    });
  }
});

// GET /api/employees - Get all employee data (combined from multiple sources) - Protected
router.get('/', authenticateToken(), async (req, res, next) => {
  try {
    console.log(`🔍 Fetching all employee data through BFF for user: ${req.user.username}...`);
    const data = await springBootService.getCombinedEmployeeData();

    res.json({
      success: true,
      data: data,
      count: Array.isArray(data) ? data.length : 1,
      requestedBy: req.user.username,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error fetching all employee data:', error);
    next({
      status: error.response?.status || 500,
      message: error.response?.data?.message || error.message || 'Failed to fetch employee data'
    });
  }
});

// GET /api/employees/:id - Get specific employee data by ID - Protected
router.get('/:id', authenticateToken(), async (req, res, next) => {
  try {
    const employeeId = parseInt(req.params.id);
    
    if (isNaN(employeeId)) {
      return next({
        status: 400,
        message: 'Invalid employee ID. Must be a number.'
      });
    }

    console.log(`🔍 Fetching employee data for ID: ${employeeId} through BFF...`);
    const data = await springBootService.getCombinedEmployeeDataById(employeeId);
    
    res.json({
      success: true,
      data: data,
      employeeId: employeeId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`❌ Error fetching employee data for ID ${req.params.id}:`, error);
    
    if (error.response?.status === 404) {
      next({
        status: 404,
        message: `Employee with ID ${req.params.id} not found`
      });
    } else {
      next({
        status: error.response?.status || 500,
        message: error.response?.data?.message || error.message || 'Failed to fetch employee data'
      });
    }
  }
});

// POST /api/employees - Insert new employee data - Admin only
router.post('/', adminOnly, async (req, res, next) => {
  try {
    console.log('📝 Inserting new employee data through BFF...', req.body);

    // Validate required fields
    const requiredFields = ['deptName', 'deptCode', 'empName', 'employeeId', 'managerName', 'experience'];
    const missingFields = requiredFields.filter(field => !req.body[field]);

    if (missingFields.length > 0) {
      return next({
        status: 400,
        message: `Missing required fields: ${missingFields.join(', ')}`
      });
    }

    // Validate employeeId is a number
    if (isNaN(parseInt(req.body.employeeId))) {
      return next({
        status: 400,
        message: 'Employee ID must be a valid number'
      });
    }

    const result = await springBootService.insertEmployeeData(req.body);

    res.status(201).json({
      success: true,
      message: 'Employee data inserted successfully',
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Error inserting employee data:', error);

    if (error.response?.status === 409) {
      next({
        status: 409,
        message: 'Employee with this ID already exists'
      });
    } else {
      next({
        status: error.response?.status || 500,
        message: error.response?.data?.message || error.message || 'Failed to insert employee data'
      });
    }
  }
});

// DELETE /api/employees/:id - Delete employee data by ID - Admin only
router.delete('/:id', adminOnly, async (req, res, next) => {
  try {
    const employeeId = parseInt(req.params.id);

    if (isNaN(employeeId)) {
      return next({
        status: 400,
        message: 'Invalid employee ID. Must be a number.'
      });
    }

    console.log(`🗑️ Deleting employee data for ID: ${employeeId} through BFF by admin: ${req.user.username}...`);
    const result = await springBootService.deleteEmployeeData(employeeId);

    res.json({
      success: true,
      message: `Employee with ID ${employeeId} and related records deleted successfully`,
      employeeId: employeeId,
      deletedBy: req.user.username,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`❌ Error deleting employee data for ID ${req.params.id}:`, error);

    if (error.response?.status === 404) {
      next({
        status: 404,
        message: `Employee with ID ${req.params.id} not found`
      });
    } else {
      next({
        status: error.response?.status || 500,
        message: error.response?.data?.message || error.message || 'Failed to delete employee data'
      });
    }
  }
});

module.exports = router;
