const jwtService = require('./jwtService');

class UserService {
  constructor() {
    // In-memory user storage (replace with database in production)
    this.users = new Map();
    this.refreshTokens = new Set();
    
    // Initialize with default admin user
    this.initializeDefaultUsers();
  }

  async initializeDefaultUsers() {
    try {
      // Create default admin user
      const adminUser = {
        userId: 'admin-001',
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123', // Will be hashed
        role: 'admin',
        createdAt: new Date().toISOString(),
        isActive: true
      };

      // Create default regular user
      const regularUser = {
        userId: 'user-001',
        username: 'user',
        email: '<EMAIL>',
        password: 'user123', // Will be hashed
        role: 'user',
        createdAt: new Date().toISOString(),
        isActive: true
      };

      await this.createUser(adminUser);
      await this.createUser(regularUser);

      console.log('✅ Default users initialized:');
      console.log('   👤 Admin: username=admin, password=admin123');
      console.log('   👤 User: username=user, password=user123');
    } catch (error) {
      console.error('❌ Error initializing default users:', error);
    }
  }

  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user (without password)
   */
  async createUser(userData) {
    try {
      // Check if user already exists
      if (this.findUserByUsername(userData.username)) {
        throw new Error('Username already exists');
      }

      if (this.findUserByEmail(userData.email)) {
        throw new Error('Email already exists');
      }

      // Hash password
      const hashedPassword = await jwtService.hashPassword(userData.password);

      // Create user object
      const user = {
        userId: userData.userId || this.generateUserId(),
        username: userData.username,
        email: userData.email,
        password: hashedPassword,
        role: userData.role || 'user',
        createdAt: userData.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isActive: userData.isActive !== undefined ? userData.isActive : true,
        lastLogin: null
      };

      // Store user
      this.users.set(user.userId, user);

      // Return user without password
      return this.sanitizeUser(user);
    } catch (error) {
      console.error('❌ Error creating user:', error);
      throw error;
    }
  }

  /**
   * Authenticate user with username/email and password
   * @param {string} identifier - Username or email
   * @param {string} password - Plain text password
   * @returns {Promise<Object>} Authentication result
   */
  async authenticateUser(identifier, password) {
    try {
      // Find user by username or email
      const user = this.findUserByUsername(identifier) || this.findUserByEmail(identifier);

      if (!user) {
        throw new Error('Invalid credentials');
      }

      if (!user.isActive) {
        throw new Error('Account is deactivated');
      }

      // Verify password
      const isPasswordValid = await jwtService.comparePassword(password, user.password);

      if (!isPasswordValid) {
        throw new Error('Invalid credentials');
      }

      // Update last login
      user.lastLogin = new Date().toISOString();
      user.updatedAt = new Date().toISOString();

      // Generate tokens
      const tokens = jwtService.generateTokenPair(user);

      // Store refresh token
      this.refreshTokens.add(tokens.refreshToken);

      return {
        user: this.sanitizeUser(user),
        tokens,
        message: 'Authentication successful'
      };
    } catch (error) {
      console.error('❌ Authentication error:', error);
      throw error;
    }
  }

  /**
   * Refresh access token
   * @param {string} refreshToken - Valid refresh token
   * @returns {Promise<Object>} New tokens
   */
  async refreshToken(refreshToken) {
    try {
      // Check if refresh token exists in our store
      if (!this.refreshTokens.has(refreshToken)) {
        throw new Error('Invalid refresh token');
      }

      // Verify and refresh token
      const newTokens = jwtService.refreshAccessToken(refreshToken);

      return {
        tokens: newTokens,
        message: 'Token refreshed successfully'
      };
    } catch (error) {
      // Remove invalid refresh token
      this.refreshTokens.delete(refreshToken);
      console.error('❌ Token refresh error:', error);
      throw error;
    }
  }

  /**
   * Logout user (invalidate refresh token)
   * @param {string} refreshToken - Refresh token to invalidate
   * @returns {Promise<Object>} Logout result
   */
  async logoutUser(refreshToken) {
    try {
      // Remove refresh token
      this.refreshTokens.delete(refreshToken);

      return {
        message: 'Logout successful'
      };
    } catch (error) {
      console.error('❌ Logout error:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param {string} userId - User ID
   * @returns {Object|null} User object (sanitized) or null
   */
  getUserById(userId) {
    const user = this.users.get(userId);
    return user ? this.sanitizeUser(user) : null;
  }

  /**
   * Find user by username
   * @param {string} username - Username
   * @returns {Object|null} User object or null
   */
  findUserByUsername(username) {
    for (const user of this.users.values()) {
      if (user.username.toLowerCase() === username.toLowerCase()) {
        return user;
      }
    }
    return null;
  }

  /**
   * Find user by email
   * @param {string} email - Email address
   * @returns {Object|null} User object or null
   */
  findUserByEmail(email) {
    for (const user of this.users.values()) {
      if (user.email.toLowerCase() === email.toLowerCase()) {
        return user;
      }
    }
    return null;
  }

  /**
   * Get all users (admin only)
   * @returns {Array} Array of sanitized user objects
   */
  getAllUsers() {
    return Array.from(this.users.values()).map(user => this.sanitizeUser(user));
  }

  /**
   * Update user
   * @param {string} userId - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated user
   */
  async updateUser(userId, updateData) {
    try {
      const user = this.users.get(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Update allowed fields
      const allowedFields = ['email', 'role', 'isActive'];
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          user[field] = updateData[field];
        }
      }

      // Update password if provided
      if (updateData.password) {
        user.password = await jwtService.hashPassword(updateData.password);
      }

      user.updatedAt = new Date().toISOString();

      return this.sanitizeUser(user);
    } catch (error) {
      console.error('❌ Error updating user:', error);
      throw error;
    }
  }

  /**
   * Remove sensitive data from user object
   * @param {Object} user - User object
   * @returns {Object} Sanitized user object
   */
  sanitizeUser(user) {
    const { password, ...sanitizedUser } = user;
    return sanitizedUser;
  }

  /**
   * Generate unique user ID
   * @returns {string} Unique user ID
   */
  generateUserId() {
    return `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Validate user data
   * @param {Object} userData - User data to validate
   * @returns {Object} Validation result
   */
  validateUserData(userData) {
    const errors = [];

    if (!userData.username || userData.username.length < 3) {
      errors.push('Username must be at least 3 characters long');
    }

    if (!userData.email || !this.isValidEmail(userData.email)) {
      errors.push('Valid email is required');
    }

    if (!userData.password || userData.password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if email is valid
   * @param {string} email - Email to validate
   * @returns {boolean} True if valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

module.exports = new UserService();
