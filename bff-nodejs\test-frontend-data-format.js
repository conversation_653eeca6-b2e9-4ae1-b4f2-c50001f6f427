#!/usr/bin/env node

const axios = require('axios');

const BFF_BASE_URL = 'http://localhost:4000';

async function testDataFormat() {
  try {
    console.log('🔐 Step 1: Login to get authentication token...');
    
    // Login first
    const loginResponse = await axios.post(`${BFF_BASE_URL}/api/auth/login`, {
      identifier: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    console.log('✅ Login successful\n');
    
    console.log('📊 Step 2: Fetch employee data...');
    
    // Get employee data with authentication
    const response = await axios.get(`${BFF_BASE_URL}/api/employees`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Data fetched successfully\n');
    
    console.log('📋 Step 3: Display data in frontend-ready format...\n');
    
    const employees = response.data.data;
    
    if (employees && employees.length > 0) {
      employees.forEach((employee, index) => {
        console.log(`Record ${index + 1}: {`);
        console.log(`  employeeId: ${employee.employeeId},`);
        console.log(`  empName: '${employee.empName}',`);
        console.log(`  deptName: '${employee.deptName}',`);
        console.log(`  deptCode: '${employee.deptCode}',`);
        console.log(`  managerName: '${employee.managerName}',`);
        console.log(`  experience: ${employee.experience}`);
        console.log(`}`);
        console.log('');
      });
      
      console.log('🎯 Frontend Integration Guide:');
      console.log('');
      console.log('In your React component, you can access the data like this:');
      console.log('');
      console.log('```javascript');
      console.log('// After successful API call');
      console.log('const employees = response.data.data;');
      console.log('');
      console.log('employees.map(employee => (');
      console.log('  <div key={employee.employeeId}>');
      console.log('    <h3>{employee.empName}</h3>');
      console.log('    <p>Department: {employee.deptName} ({employee.deptCode})</p>');
      console.log('    <p>Manager: {employee.managerName}</p>');
      console.log('    <p>Experience: {employee.experience} years</p>');
      console.log('  </div>');
      console.log('));');
      console.log('```');
      
    } else {
      console.log('❌ No employee data found');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data?.error?.message || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n🔐 Authentication required! Make sure your frontend:');
      console.log('1. Calls /api/auth/login first');
      console.log('2. Stores the JWT token');
      console.log('3. Includes token in Authorization header for API calls');
    }
  }
}

console.log('🧪 Testing Frontend Data Format...\n');
testDataFormat();
