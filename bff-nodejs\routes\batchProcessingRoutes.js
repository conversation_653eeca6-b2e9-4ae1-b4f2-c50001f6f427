const express = require('express');
const router = express.Router();
const batchProcessingService = require('../services/batchProcessingService');
const { authenticateToken } = require('../middleware/authMiddleware');
const rateLimit = require('express-rate-limit');
const Joi = require('joi');

// Rate limiting for batch processing endpoints
const batchProcessingLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit each IP to 50 requests per windowMs
  message: {
    error: {
      message: 'Too many batch processing requests from this IP, please try again later.',
      status: 429,
      timestamp: new Date().toISOString()
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation schemas
const sessionIdSchema = Joi.object({
  sessionId: Joi.string().required().min(1).max(100)
});

// Apply rate limiting and authentication to all routes
router.use(batchProcessingLimiter);
router.use(authenticateToken()); // Ensure user is authenticated

/**
 * @route   GET /api/batch-processing/first-batch
 * @desc    Get first batch of employee data (first 2 items)
 * @access  Private
 */
router.get('/first-batch', async (req, res) => {
  try {
    console.log('🚀 BFF: Starting first batch processing request');
    
    const startTime = Date.now();
    const result = await batchProcessingService.getFirstBatch();
    const processingTime = Date.now() - startTime;
    
    // Add BFF-specific metadata
    const response = {
      ...result,
      _bffInfo: {
        processingTimeMs: processingTime,
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`,
        userId: req.user?.id || 'anonymous',
        timestamp: new Date().toISOString()
      }
    };
    
    console.log(`✅ BFF: First batch processed successfully in ${processingTime}ms`);
    res.json(response);
    
  } catch (error) {
    console.error('❌ BFF: Error in first batch processing:', error);
    res.status(error.status || 500).json({
      error: {
        message: error.message || 'Failed to process first batch',
        status: error.status || 500,
        service: 'bff-batch-processing',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`
      }
    });
  }
});

/**
 * @route   GET /api/batch-processing/remaining-data
 * @desc    Get remaining employee data using session ID
 * @access  Private
 */
router.get('/remaining-data', async (req, res) => {
  try {
    // Validate session ID
    const { error, value } = sessionIdSchema.validate({ sessionId: req.query.sessionId });
    if (error) {
      return res.status(400).json({
        error: {
          message: 'Invalid session ID',
          details: error.details[0].message,
          status: 400,
          timestamp: new Date().toISOString()
        }
      });
    }
    
    const { sessionId } = value;
    console.log(`🚀 BFF: Getting remaining data for session: ${sessionId}`);
    
    const startTime = Date.now();
    const result = await batchProcessingService.getRemainingData(sessionId);
    const processingTime = Date.now() - startTime;
    
    // Add BFF-specific metadata
    const response = {
      ...result,
      _bffInfo: {
        processingTimeMs: processingTime,
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`,
        userId: req.user?.id || 'anonymous',
        timestamp: new Date().toISOString()
      }
    };
    
    console.log(`✅ BFF: Remaining data processed successfully in ${processingTime}ms`);
    res.json(response);
    
  } catch (error) {
    console.error('❌ BFF: Error getting remaining data:', error);
    res.status(error.status || 500).json({
      error: {
        message: error.message || 'Failed to get remaining data',
        status: error.status || 500,
        service: 'bff-batch-processing',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`
      }
    });
  }
});

/**
 * @route   GET /api/batch-processing/session-status
 * @desc    Check session status
 * @access  Private
 */
router.get('/session-status', async (req, res) => {
  try {
    // Validate session ID
    const { error, value } = sessionIdSchema.validate({ sessionId: req.query.sessionId });
    if (error) {
      return res.status(400).json({
        error: {
          message: 'Invalid session ID',
          details: error.details[0].message,
          status: 400,
          timestamp: new Date().toISOString()
        }
      });
    }
    
    const { sessionId } = value;
    console.log(`🚀 BFF: Checking session status: ${sessionId}`);
    
    const startTime = Date.now();
    const result = await batchProcessingService.getSessionStatus(sessionId);
    const processingTime = Date.now() - startTime;
    
    // Add BFF-specific metadata
    const response = {
      ...result,
      _bffInfo: {
        processingTimeMs: processingTime,
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`,
        userId: req.user?.id || 'anonymous',
        timestamp: new Date().toISOString()
      }
    };
    
    console.log(`✅ BFF: Session status checked successfully in ${processingTime}ms`);
    res.json(response);
    
  } catch (error) {
    console.error('❌ BFF: Error checking session status:', error);
    res.status(error.status || 500).json({
      error: {
        message: error.message || 'Failed to check session status',
        status: error.status || 500,
        service: 'bff-batch-processing',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`
      }
    });
  }
});

/**
 * @route   GET /api/batch-processing/batch-controlled
 * @desc    Get batch controlled data (alternative endpoint - first 2 items only)
 * @access  Private
 */
router.get('/batch-controlled', async (req, res) => {
  try {
    console.log('🚀 BFF: Getting batch controlled data');
    
    const startTime = Date.now();
    const result = await batchProcessingService.getBatchControlledData();
    const processingTime = Date.now() - startTime;
    
    // Add BFF-specific metadata
    const response = {
      data: result,
      _bffInfo: {
        processingTimeMs: processingTime,
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`,
        userId: req.user?.id || 'anonymous',
        timestamp: new Date().toISOString(),
        itemCount: Array.isArray(result) ? result.length : 1
      }
    };
    
    console.log(`✅ BFF: Batch controlled data processed successfully in ${processingTime}ms`);
    res.json(response);
    
  } catch (error) {
    console.error('❌ BFF: Error getting batch controlled data:', error);
    res.status(error.status || 500).json({
      error: {
        message: error.message || 'Failed to get batch controlled data',
        status: error.status || 500,
        service: 'bff-batch-processing',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`
      }
    });
  }
});

/**
 * @route   GET /api/batch-processing/updated-employee-data
 * @desc    Get updated employee data (first 2 items using improved endpoint)
 * @access  Private
 */
router.get('/updated-employee-data', async (req, res) => {
  try {
    console.log('🚀 BFF: Getting updated employee data');
    
    const startTime = Date.now();
    const result = await batchProcessingService.getUpdatedEmployeeData();
    const processingTime = Date.now() - startTime;
    
    // Add BFF-specific metadata
    const response = {
      data: result,
      _bffInfo: {
        processingTimeMs: processingTime,
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`,
        userId: req.user?.id || 'anonymous',
        timestamp: new Date().toISOString(),
        itemCount: Array.isArray(result) ? result.length : 1
      }
    };
    
    console.log(`✅ BFF: Updated employee data processed successfully in ${processingTime}ms`);
    res.json(response);
    
  } catch (error) {
    console.error('❌ BFF: Error getting updated employee data:', error);
    res.status(error.status || 500).json({
      error: {
        message: error.message || 'Failed to get updated employee data',
        status: error.status || 500,
        service: 'bff-batch-processing',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || `req-${Date.now()}`
      }
    });
  }
});

/**
 * @route   GET /api/batch-processing/health
 * @desc    Health check for batch processing service
 * @access  Private
 */
router.get('/health', async (req, res) => {
  try {
    console.log('🚀 BFF: Checking batch processing service health');
    
    const startTime = Date.now();
    const result = await batchProcessingService.healthCheck();
    const processingTime = Date.now() - startTime;
    
    const response = {
      ...result,
      bffHealth: {
        status: 'UP',
        processingTimeMs: processingTime,
        timestamp: new Date().toISOString()
      }
    };
    
    console.log(`✅ BFF: Health check completed in ${processingTime}ms`);
    res.json(response);
    
  } catch (error) {
    console.error('❌ BFF: Error in health check:', error);
    res.status(500).json({
      status: 'DOWN',
      error: error.message,
      bffHealth: {
        status: 'DOWN',
        timestamp: new Date().toISOString()
      }
    });
  }
});

/**
 * @route   GET /api/batch-processing/info
 * @desc    Get information about available batch processing endpoints
 * @access  Private
 */
router.get('/info', (req, res) => {
  const endpoints = {
    service: 'BFF Batch Processing API',
    version: '1.0.0',
    endpoints: [
      {
        path: '/api/batch-processing/first-batch',
        method: 'GET',
        description: 'Get first batch of employee data (first 2 items)',
        authentication: 'Required'
      },
      {
        path: '/api/batch-processing/remaining-data',
        method: 'GET',
        description: 'Get remaining employee data using session ID',
        parameters: ['sessionId (query)'],
        authentication: 'Required'
      },
      {
        path: '/api/batch-processing/session-status',
        method: 'GET',
        description: 'Check session status',
        parameters: ['sessionId (query)'],
        authentication: 'Required'
      },
      {
        path: '/api/batch-processing/batch-controlled',
        method: 'GET',
        description: 'Get batch controlled data (first 2 items only)',
        authentication: 'Required'
      },
      {
        path: '/api/batch-processing/updated-employee-data',
        method: 'GET',
        description: 'Get updated employee data (improved endpoint)',
        authentication: 'Required'
      },
      {
        path: '/api/batch-processing/health',
        method: 'GET',
        description: 'Health check for batch processing service',
        authentication: 'Required'
      }
    ],
    rateLimit: {
      windowMs: '15 minutes',
      maxRequests: 50,
      message: 'Rate limit applied per IP address'
    },
    timestamp: new Date().toISOString()
  };
  
  res.json(endpoints);
});

module.exports = router;
