#!/usr/bin/env node

const axios = require('axios');

const BFF_BASE_URL = 'http://localhost:4000';
const SPRING_BOOT_URL = 'http://localhost:8080';

console.log('🧪 Testing BFF Setup...\n');

async function testBFFHealth() {
  try {
    console.log('1️⃣ Testing BFF Health Check...');
    const response = await axios.get(`${BFF_BASE_URL}/api/health`);
    console.log('✅ BFF Health Check:', response.data.status);
    return true;
  } catch (error) {
    console.log('❌ BFF Health Check Failed:', error.message);
    return false;
  }
}

async function testSpringBootConnection() {
  try {
    console.log('2️⃣ Testing Spring Boot Connection...');
    const response = await axios.get(`${SPRING_BOOT_URL}/employee-department-manager/employee-data-list`);
    console.log('✅ Spring Boot Connection: OK');
    return true;
  } catch (error) {
    console.log('❌ Spring Boot Connection Failed:', error.message);
    return false;
  }
}

async function testBFFEmployeeEndpoint() {
  try {
    console.log('3️⃣ Testing BFF Employee Endpoint...');
    const response = await axios.get(`${BFF_BASE_URL}/api/employees`);
    console.log('✅ BFF Employee Endpoint: OK');
    console.log(`   📊 Retrieved ${response.data.count || 'unknown'} records`);
    return true;
  } catch (error) {
    console.log('❌ BFF Employee Endpoint Failed:', error.message);
    return false;
  }
}

async function testBFFEmployeeById() {
  try {
    console.log('4️⃣ Testing BFF Employee By ID...');
    // Try to get employee with ID 101 (common test ID)
    const response = await axios.get(`${BFF_BASE_URL}/api/employees/101`);
    console.log('✅ BFF Employee By ID: OK');
    return true;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('⚠️ BFF Employee By ID: No employee with ID 101 found (this is OK)');
      return true;
    }
    console.log('❌ BFF Employee By ID Failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting BFF Integration Tests\n');
  
  const tests = [
    testBFFHealth,
    testSpringBootConnection,
    testBFFEmployeeEndpoint,
    testBFFEmployeeById
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    const result = await test();
    if (result) {
      passed++;
    } else {
      failed++;
    }
    console.log(''); // Add spacing
  }

  console.log('📊 Test Results:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%\n`);

  if (failed === 0) {
    console.log('🎉 All tests passed! Your BFF setup is working correctly.');
    console.log('🌐 You can now access your React app at http://localhost:3000');
  } else {
    console.log('⚠️ Some tests failed. Please check the following:');
    console.log('   1. Is the BFF server running on port 4000?');
    console.log('   2. Is the Spring Boot server running on port 8080?');
    console.log('   3. Are there any CORS issues?');
    console.log('   4. Check the server logs for more details.');
  }

  process.exit(failed > 0 ? 1 : 0);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(1);
});

// Run the tests
runTests().catch(error => {
  console.error('💥 Unexpected error during testing:', error);
  process.exit(1);
});
