#!/usr/bin/env node

const axios = require('axios');

const BFF_BASE_URL = 'http://localhost:4000';

async function testDeleteFunctionality() {
  console.log('🗑️ Testing DELETE Employee Functionality...\n');

  try {
    // Step 1: Login as admin
    console.log('🔐 Step 1: Login as admin...');
    const loginResponse = await axios.post(`${BFF_BASE_URL}/api/auth/login`, {
      identifier: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    console.log('✅ Admin login successful\n');

    // Step 2: Get current employee list
    console.log('📊 Step 2: Getting current employee list...');
    const listResponse = await axios.get(`${BFF_BASE_URL}/api/employees`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const employees = listResponse.data.data;
    console.log(`✅ Found ${employees.length} employees`);
    
    if (employees.length > 0) {
      console.log('📋 Current employees:');
      employees.forEach(emp => {
        console.log(`   - ID: ${emp.employeeId}, Name: ${emp.empName}, Dept: ${emp.deptName}`);
      });
    }
    console.log('');

    // Step 3: Test DELETE with a specific employee ID
    const testEmployeeId = 101; // You can change this to any existing employee ID
    console.log(`🗑️ Step 3: Testing DELETE for employee ID: ${testEmployeeId}...`);
    
    try {
      const deleteResponse = await axios.delete(`${BFF_BASE_URL}/api/employees/${testEmployeeId}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      console.log('✅ DELETE request successful!');
      console.log('📋 Response:', {
        success: deleteResponse.data.success,
        message: deleteResponse.data.message,
        employeeId: deleteResponse.data.employeeId,
        deletedBy: deleteResponse.data.deletedBy
      });
      
    } catch (deleteError) {
      if (deleteError.response?.status === 404) {
        console.log(`⚠️ Employee ID ${testEmployeeId} not found (this is expected if already deleted)`);
        console.log('📋 Response:', deleteError.response.data);
      } else {
        throw deleteError;
      }
    }
    console.log('');

    // Step 4: Verify employee was deleted
    console.log('🔍 Step 4: Verifying employee was deleted...');
    const verifyResponse = await axios.get(`${BFF_BASE_URL}/api/employees`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const remainingEmployees = verifyResponse.data.data;
    const deletedEmployee = remainingEmployees.find(emp => emp.employeeId === testEmployeeId);
    
    if (!deletedEmployee) {
      console.log(`✅ Employee ID ${testEmployeeId} successfully deleted from the list`);
    } else {
      console.log(`⚠️ Employee ID ${testEmployeeId} still exists in the list`);
    }
    
    console.log(`📊 Remaining employees: ${remainingEmployees.length}`);
    console.log('');

    // Step 5: Test DELETE with non-existent ID
    console.log('🧪 Step 5: Testing DELETE with non-existent ID...');
    try {
      await axios.delete(`${BFF_BASE_URL}/api/employees/99999`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ DELETE should have failed for non-existent ID');
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ Correctly returned 404 for non-existent employee');
        console.log('📋 Error message:', error.response.data.error.message);
      } else {
        console.log('⚠️ Unexpected error:', error.response?.data || error.message);
      }
    }
    console.log('');

    // Step 6: Test DELETE with invalid ID
    console.log('🧪 Step 6: Testing DELETE with invalid ID...');
    try {
      await axios.delete(`${BFF_BASE_URL}/api/employees/invalid`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('❌ DELETE should have failed for invalid ID');
    } catch (error) {
      if (error.response?.status === 400) {
        console.log('✅ Correctly returned 400 for invalid employee ID');
        console.log('📋 Error message:', error.response.data.error.message);
      } else {
        console.log('⚠️ Unexpected error:', error.response?.data || error.message);
      }
    }
    console.log('');

    // Step 7: Test DELETE without admin privileges
    console.log('🔐 Step 7: Testing DELETE without admin privileges...');
    
    // Login as regular user
    const userLoginResponse = await axios.post(`${BFF_BASE_URL}/api/auth/login`, {
      identifier: 'user',
      password: 'user123'
    });
    
    const userToken = userLoginResponse.data.data.tokens.accessToken;
    
    try {
      await axios.delete(`${BFF_BASE_URL}/api/employees/102`, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log('❌ DELETE should have failed for non-admin user');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Correctly blocked non-admin user from deleting');
        console.log('📋 Error message:', error.response.data.error.message);
      } else {
        console.log('⚠️ Unexpected error:', error.response?.data || error.message);
      }
    }

    console.log('\n🎉 DELETE functionality testing completed!');
    console.log('\n📋 Frontend Integration Example:');
    console.log('```javascript');
    console.log('// Delete employee function for React frontend');
    console.log('const deleteEmployee = async (employeeId) => {');
    console.log('  try {');
    console.log('    const token = localStorage.getItem("authToken");');
    console.log('    const response = await fetch(`http://localhost:4000/api/employees/${employeeId}`, {');
    console.log('      method: "DELETE",');
    console.log('      headers: {');
    console.log('        "Authorization": `Bearer ${token}`,');
    console.log('        "Content-Type": "application/json"');
    console.log('      }');
    console.log('    });');
    console.log('    ');
    console.log('    if (response.ok) {');
    console.log('      const result = await response.json();');
    console.log('      console.log("Employee deleted:", result.message);');
    console.log('      // Refresh employee list');
    console.log('      fetchEmployees();');
    console.log('    } else {');
    console.log('      const error = await response.json();');
    console.log('      console.error("Delete failed:", error.error.message);');
    console.log('    }');
    console.log('  } catch (error) {');
    console.log('    console.error("Delete error:", error);');
    console.log('  }');
    console.log('};');
    console.log('```');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testDeleteFunctionality();
