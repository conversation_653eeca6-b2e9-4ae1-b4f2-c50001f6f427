const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

const employeeRoutes = require('./routes/employeeRoutes');
const healthRoutes = require('./routes/healthRoutes');
const authRoutes = require('./routes/authRoutes');
const batchProcessingRoutes = require('./routes/batchProcessingRoutes');

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware
app.use(helmet());
app.use(morgan('combined'));
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:4200', // Updated for Angular default port
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-request-id']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add request ID middleware for better tracing
app.use((req, res, next) => {
  req.headers['x-request-id'] = req.headers['x-request-id'] || `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  res.setHeader('x-request-id', req.headers['x-request-id']);
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/health', healthRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/batch-processing', batchProcessingRoutes); // New batch processing routes

// Root endpoint with API information
app.get('/', (req, res) => {
  res.json({
    service: 'Employee Management BFF',
    version: '1.0.0',
    description: 'Backend for Frontend service for Employee Management System',
    endpoints: {
      auth: '/api/auth',
      employees: '/api/employees',
      batchProcessing: '/api/batch-processing',
      health: '/api/health'
    },
    documentation: {
      batchProcessing: '/api/batch-processing/info',
      health: '/api/health'
    },
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  // Enhanced error response with request tracking
  const errorResponse = {
    error: {
      message: err.message || 'Internal Server Error',
      status: err.status || 500,
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'],
      service: 'bff-nodejs'
    }
  };
  
  // Add stack trace in development
  if (process.env.NODE_ENV === 'development') {
    errorResponse.error.stack = err.stack;
  }
  
  res.status(err.status || 500).json(errorResponse);
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      message: 'Route not found',
      status: 404,
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'],
      path: req.originalUrl,
      method: req.method,
      availableEndpoints: [
        '/api/auth',
        '/api/employees', 
        '/api/batch-processing',
        '/api/health'
      ]
    }
  });
});

app.listen(PORT, () => {
  console.log(`🚀 BFF Server running on port ${PORT}`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:4200'}`);
  console.log(`🔧 Employee Service: ${process.env.EMPLOYEE_SERVICE_URL || 'http://localhost:8080'}`);
  console.log(`📦 Batch Service: ${process.env.BATCH_SERVICE_URL || 'http://localhost:8080/proxy'}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api/batch-processing/info`);
});

module.exports = app;
