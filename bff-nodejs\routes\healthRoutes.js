const express = require('express');
const router = express.Router();
const springBootService = require('../services/springBootService');

// GET /api/health - Health check for BFF and downstream services
router.get('/', async (req, res) => {
  try {
    const bffHealth = {
      status: 'UP',
      service: 'bff-nodejs',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    };

    // Check downstream services
    const springBootHealth = await springBootService.healthCheck();

    const overallStatus = springBootHealth.status === 'UP' ? 'UP' : 'DEGRADED';

    res.json({
      status: overallStatus,
      services: {
        bff: bffHealth,
        springBoot: springBootHealth
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Health check error:', error);
    res.status(503).json({
      status: 'DOWN',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/health/ready - Readiness probe
router.get('/ready', async (req, res) => {
  try {
    // Check if all required services are available
    await springBootService.healthCheck();
    
    res.json({
      status: 'READY',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({
      status: 'NOT_READY',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// GET /api/health/live - Liveness probe
router.get('/live', (req, res) => {
  res.json({
    status: 'ALIVE',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

module.exports = router;
