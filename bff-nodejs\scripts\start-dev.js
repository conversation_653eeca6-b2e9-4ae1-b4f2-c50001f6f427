#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Employee Management BFF in Development Mode...\n');

// Check if required environment variables are set
const requiredEnvVars = ['EMPLOYEE_SERVICE_URL', 'FRONTEND_URL'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.log('⚠️  Warning: Missing environment variables:');
  missingEnvVars.forEach(envVar => {
    console.log(`   - ${envVar}`);
  });
  console.log('   Using default values from .env file\n');
}

// Start the server with nodemon
const serverPath = path.join(__dirname, '..', 'server.js');
const nodemon = spawn('nodemon', [serverPath], {
  stdio: 'inherit',
  env: { ...process.env, NODE_ENV: 'development' }
});

nodemon.on('close', (code) => {
  console.log(`\n🛑 BFF server exited with code ${code}`);
  process.exit(code);
});

nodemon.on('error', (err) => {
  console.error('❌ Failed to start BFF server:', err);
  process.exit(1);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down BFF server...');
  nodemon.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down BFF server...');
  nodemon.kill('SIGTERM');
});
