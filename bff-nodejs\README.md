# Employee Management BFF (Backend For Frontend)

This Node.js Express server acts as a Backend For Frontend (BFF) layer between the React frontend and Spring Boot microservices.

## Architecture

```
React Frontend (Port 3000)
    ↓
Node.js BFF (Port 4000)
    ↓
Spring Boot Services (Port 8080)
```

## Features

- **🔐 JWT Authentication**: Secure token-based authentication with refresh tokens
- **👥 User Management**: In-memory user storage with role-based access control
- **🔄 API Aggregation**: Combines data from multiple Spring Boot microservices
- **🔧 Data Transformation**: Converts nested backend data to flat frontend structure
- **❌ Error Handling**: Centralized error handling and transformation
- **🔁 Retry Logic**: Automatic retry for failed requests to backend services
- **💚 Health Checks**: Monitors the health of downstream services
- **🌐 CORS Configuration**: Handles cross-origin requests from React frontend
- **📝 Logging**: Comprehensive request/response logging
- **🛡️ Rate Limiting**: Request rate limiting and validation

## Installation

1. Navigate to the BFF directory:
```bash
cd bff-nodejs
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

## Validation & Testing

### Data Format Validation

To validate that the BFF is returning data in the correct format for your frontend:

```bash
# Validate API data format (requires BFF server to be running)
node validate-data-format.js
```

This validation script will:
- ✅ Test authentication flow
- ✅ Verify response structure
- ✅ Check all required fields are present
- ✅ Confirm flat data structure (no nested objects)
- ✅ Validate data types
- ✅ Show sample frontend integration code

### Other Test Scripts

```bash
# Test data transformation logic
node test-data-transformation.js

# Test authenticated endpoints
node test-authenticated-endpoints.js

# Test frontend data format
node test-frontend-data-format.js

# Test DELETE functionality
node test-delete-functionality.js
```

## Running the BFF

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

The BFF server will start on `http://localhost:4000`

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login with username/password
- `POST /api/auth/register` - Register new user (admin only)
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout user

**Default Users:**
- Admin: `admin` / `admin123`
- User: `user` / `user123`

### Employee Management (🔐 Protected - Requires JWT Token)
- `GET /api/employees` - Get all employee data (aggregated)
- `GET /api/employees/:id` - Get specific employee data
- `POST /api/employees` - Insert new employee data (admin only)
- `DELETE /api/employees/:id` - Delete employee data by ID (admin only)
- `GET /api/employees/search/:query` - Search employees

### Health Checks
- `GET /api/health` - Overall health status
- `GET /api/health/ready` - Readiness probe
- `GET /api/health/live` - Liveness probe

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | BFF server port | 4000 |
| `NODE_ENV` | Environment | development |
| `EMPLOYEE_SERVICE_URL` | Spring Boot service URL | http://localhost:8080/employee-department-manager |
| `FRONTEND_URL` | React frontend URL | http://localhost:3000 |
| `API_TIMEOUT` | API request timeout (ms) | 10000 |
| `RETRY_ATTEMPTS` | Number of retry attempts | 3 |

## Data Flow

1. **Authentication**: Frontend authenticates with BFF to get JWT token
2. **Frontend Request**: React app sends authenticated request to BFF
3. **BFF Processing**: BFF validates JWT and calls appropriate Spring Boot services
4. **Data Aggregation**: BFF combines data from multiple sources if needed
5. **Response Transformation**: BFF transforms nested data to flat structure for frontend
6. **Frontend Response**: BFF sends processed data back to React app

### Data Transformation Example

**Backend Response (Nested):**
```json
{
  "employee": { "employeeId": 101, "name": "John Doe" },
  "department": { "name": "Engineering", "code": "ENG-01" },
  "manager": { "name": "Jane Smith", "experience": 10 }
}
```

**Frontend Response (Flat):**
```json
{
  "employeeId": 101,
  "empName": "John Doe",
  "deptName": "Engineering",
  "deptCode": "ENG-01",
  "managerName": "Jane Smith",
  "experience": 10
}
```

## Authentication Usage

### Frontend Integration Example

```javascript
// 1. Login to get JWT token
const loginResponse = await fetch('http://localhost:4000/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    identifier: 'admin',
    password: 'admin123'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.tokens.accessToken;

// 2. Use token for API calls
const response = await fetch('http://localhost:4000/api/employees', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
const employees = result.data; // Ready for frontend consumption!

// 3. Delete employee (admin only)
const deleteEmployee = async (employeeId) => {
  const response = await fetch(`http://localhost:4000/api/employees/${employeeId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (response.ok) {
    const result = await response.json();
    console.log('Employee deleted:', result.message);
  }
};
```

## Error Handling

The BFF provides standardized error responses:

```json
{
  "error": {
    "message": "Error description",
    "status": 500,
    "timestamp": "2023-12-07T10:30:00.000Z"
  }
}
```

### Authentication Errors

```json
{
  "error": {
    "message": "Access token is required",
    "status": 401,
    "code": "TOKEN_MISSING",
    "timestamp": "2025-07-03T11:37:38.132Z"
  }
}
```

## Monitoring

### Health Check Response
```json
{
  "status": "UP",
  "services": {
    "bff": {
      "status": "UP",
      "uptime": 3600,
      "memory": {...}
    },
    "springBoot": {
      "status": "UP",
      "service": "employee-service"
    }
  }
}
```

## Development

### Adding New Services

1. Create a new service class in `services/`
2. Add routes in `routes/`
3. Update the main server configuration

### Testing

```bash
# Run tests (when implemented)
npm test

# Check health
curl http://localhost:4000/api/health
```

## Deployment

### Docker (Future Enhancement)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 4000
CMD ["npm", "start"]
```

### Environment-Specific Configuration
- Development: Uses nodemon for auto-restart
- Production: Uses PM2 or similar process manager

## Benefits of BFF Pattern

1. **Frontend Optimization**: Tailored APIs for React app needs
2. **Reduced Network Calls**: Aggregates multiple backend calls
3. **Simplified Frontend**: Frontend doesn't need to handle complex backend logic
4. **Centralized Error Handling**: Consistent error responses
5. **Security**: Additional security layer between frontend and backend
6. **Performance**: Caching and optimization opportunities
7. **Flexibility**: Easy to modify without changing backend services
