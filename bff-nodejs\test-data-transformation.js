#!/usr/bin/env node

// Test script to verify data transformation
const springBootService = require('./services/springBootService');

// Sample data structure from your backend
const sampleData = [
  {
    department: {
      id: '685ed149deefef5784e09e98',
      name: 'Engineering',
      code: 'ENG-01',
      employeeId: 101
    },
    employee: {
      id: '685ed14adeefef5784e09e99',
      name: '<PERSON>',
      employeeId: 101
    },
    manager: {
      id: '685ed14adeefef5784e09e9a',
      name: '<PERSON>',
      experience: 10,
      employeeId: 101
    }
  },
  {
    department: {
      id: '685ed1efdeefef5784e09e9b',
      name: 'IT',
      code: 'IT-01',
      employeeId: 1
    },
    employee: {
      id: '685ed1efdeefef5784e09e9c',
      name: '<PERSON><PERSON><PERSON>',
      employeeId: 1
    },
    manager: {
      id: '685ed1efdeefef5784e09e9d',
      name: '<PERSON>',
      experience: 15,
      employeeId: 1
    }
  }
];

console.log('🧪 Testing Data Transformation...\n');

console.log('📥 Original Data Structure:');
console.log(JSON.stringify(sampleData[0], null, 2));

console.log('\n📤 Transformed Data Structure:');
const transformed = springBootService.transformEmployeeData(sampleData);
console.log(JSON.stringify(transformed[0], null, 2));

console.log('\n✅ Expected Frontend Fields:');
console.log('- employeeId:', transformed[0].employeeId);
console.log('- empName:', transformed[0].empName);
console.log('- deptName:', transformed[0].deptName);
console.log('- deptCode:', transformed[0].deptCode);
console.log('- managerName:', transformed[0].managerName);
console.log('- experience:', transformed[0].experience);

console.log('\n🎯 All transformed records:');
transformed.forEach((record, index) => {
  console.log(`Record ${index + 1}:`, {
    employeeId: record.employeeId,
    empName: record.empName,
    deptName: record.deptName,
    deptCode: record.deptCode,
    managerName: record.managerName,
    experience: record.experience
  });
});
