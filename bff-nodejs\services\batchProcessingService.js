const axios = require('axios');

class BatchProcessingService {
  constructor() {
    this.batchServiceUrl = process.env.BATCH_SERVICE_URL || 'http://localhost:8080/proxy';
    this.timeout = parseInt(process.env.API_TIMEOUT) || 15000; // Longer timeout for batch processing
    this.retryAttempts = parseInt(process.env.RETRY_ATTEMPTS) || 3;

    // Create axios instance for batch processing service
    this.batchClient = axios.create({
      baseURL: this.batchServiceUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    // Setup interceptors for logging and error handling
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.batchClient.interceptors.request.use(
      (config) => {
        console.log(`🔄 Batch API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ Batch API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.batchClient.interceptors.response.use(
      (response) => {
        console.log(`✅ Batch API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error(`❌ Batch API Error: ${error.config?.url}`, error.message);
        return Promise.reject(error);
      }
    );
  }

  // Retry mechanism for failed requests
  async retryRequest(requestFn, attempts = this.retryAttempts) {
    try {
      return await requestFn();
    } catch (error) {
      if (attempts > 1 && this.isRetryableError(error)) {
        console.log(`🔄 Retrying batch request... ${attempts - 1} attempts left`);
        await this.delay(1000);
        return this.retryRequest(requestFn, attempts - 1);
      }
      throw error;
    }
  }

  isRetryableError(error) {
    return error.code === 'ECONNRESET' ||
           error.code === 'ETIMEDOUT' ||
           (error.response && error.response.status >= 500);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get first batch of employee data (first 2 items)
   * Returns session ID for subsequent requests
   */
  async getFirstBatch() {
    try {
      return await this.retryRequest(async () => {
        const response = await this.batchClient.get('/employee-data-first-batch');
        
        // Transform and enrich the response
        const transformedData = this.transformFirstBatchResponse(response.data);
        
        console.log(`📦 First batch processed: ${transformedData.processedItems} items, Session: ${transformedData.sessionId}`);
        return transformedData;
      });
    } catch (error) {
      console.error('❌ Error getting first batch:', error.message);
      
      // Return mock data for development/testing
      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️ Returning mock first batch data for development');
        return this.getMockFirstBatchData();
      }
      
      throw this.createBatchProcessingError('Failed to get first batch', error);
    }
  }

  /**
   * Get remaining employee data using session ID
   */
  async getRemainingData(sessionId) {
    try {
      if (!sessionId) {
        throw new Error('Session ID is required');
      }

      return await this.retryRequest(async () => {
        const response = await this.batchClient.get(`/employee-data-remaining?sessionId=${sessionId}`);
        
        // Transform and enrich the response
        const transformedData = this.transformRemainingDataResponse(response.data);
        
        console.log(`📦 Remaining data processed: ${transformedData.processedItems} items for session: ${sessionId}`);
        return transformedData;
      });
    } catch (error) {
      console.error(`❌ Error getting remaining data for session ${sessionId}:`, error.message);
      
      // Return mock data for development/testing
      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️ Returning mock remaining data for development');
        return this.getMockRemainingData(sessionId);
      }
      
      throw this.createBatchProcessingError('Failed to get remaining data', error);
    }
  }

  /**
   * Check session status
   */
  async getSessionStatus(sessionId) {
    try {
      if (!sessionId) {
        throw new Error('Session ID is required');
      }

      return await this.retryRequest(async () => {
        const response = await this.batchClient.get(`/session-status?sessionId=${sessionId}`);
        
        const transformedData = this.transformSessionStatusResponse(response.data);
        
        console.log(`📊 Session status checked: ${sessionId} - Exists: ${transformedData.exists}`);
        return transformedData;
      });
    } catch (error) {
      console.error(`❌ Error checking session status for ${sessionId}:`, error.message);
      
      // Return mock status for development/testing
      if (process.env.NODE_ENV === 'development') {
        return this.getMockSessionStatus(sessionId);
      }
      
      throw this.createBatchProcessingError('Failed to check session status', error);
    }
  }

  /**
   * Get batch controlled data (alternative endpoint - first 2 items only)
   */
  async getBatchControlledData() {
    try {
      return await this.retryRequest(async () => {
        const response = await this.batchClient.get('/employee-data-batch-controlled');
        
        const transformedData = this.transformBatchControlledResponse(response.data);
        
        console.log(`⚡ Batch controlled data processed: ${transformedData.length} items`);
        return transformedData;
      });
    } catch (error) {
      console.error('❌ Error getting batch controlled data:', error.message);
      
      // Return mock data for development/testing
      if (process.env.NODE_ENV === 'development') {
        console.warn('⚠️ Returning mock batch controlled data for development');
        return this.getMockBatchControlledData();
      }
      
      throw this.createBatchProcessingError('Failed to get batch controlled data', error);
    }
  }

  /**
   * Get updated employee data (first 2 items using improved endpoint)
   */
  async getUpdatedEmployeeData() {
    try {
      return await this.retryRequest(async () => {
        const response = await this.batchClient.get('/employee-data3');
        
        const transformedData = this.transformBatchControlledResponse(response.data);
        
        console.log(`🔧 Updated employee data processed: ${transformedData.length} items`);
        return transformedData;
      });
    } catch (error) {
      console.error('❌ Error getting updated employee data:', error.message);
      
      // Return mock data for development/testing
      if (process.env.NODE_ENV === 'development') {
        return this.getMockBatchControlledData();
      }
      
      throw this.createBatchProcessingError('Failed to get updated employee data', error);
    }
  }

  // Data transformation methods
  transformFirstBatchResponse(data) {
    return {
      sessionId: data.sessionId,
      firstBatch: this.enrichEmployeeDataArray(data.firstBatch || []),
      totalItems: data.totalItems || 0,
      processedItems: data.processedItems || 0,
      remainingItems: data.remainingItems || 0,
      hasMoreData: data.hasMoreData || false,
      _metadata: {
        source: 'batch-processing-service',
        endpoint: 'first-batch',
        timestamp: new Date().toISOString(),
        transformedBy: 'bff-nodejs'
      }
    };
  }

  transformRemainingDataResponse(data) {
    return {
      sessionId: data.sessionId,
      remainingData: this.enrichEmployeeDataArray(data.remainingData || []),
      processedItems: data.processedItems || 0,
      hasMoreData: data.hasMoreData || false,
      message: data.message || 'Remaining data processed successfully',
      _metadata: {
        source: 'batch-processing-service',
        endpoint: 'remaining-data',
        timestamp: new Date().toISOString(),
        transformedBy: 'bff-nodejs'
      }
    };
  }

  transformSessionStatusResponse(data) {
    return {
      sessionId: data.sessionId,
      exists: data.exists || false,
      remainingCount: data.remainingCount || 0,
      hasMoreData: data.hasMoreData || false,
      _metadata: {
        source: 'batch-processing-service',
        endpoint: 'session-status',
        timestamp: new Date().toISOString(),
        transformedBy: 'bff-nodejs'
      }
    };
  }

  transformBatchControlledResponse(data) {
    return this.enrichEmployeeDataArray(Array.isArray(data) ? data : [data]);
  }

  enrichEmployeeDataArray(dataArray) {
    return dataArray.map(item => this.enrichEmployeeRecord(item));
  }

  enrichEmployeeRecord(record) {
    // Add BFF-specific enrichments
    return {
      ...record,
      _bffMetadata: {
        processedAt: new Date().toISOString(),
        source: 'batch-processing-via-bff',
        version: '1.0'
      }
    };
  }

  // Mock data methods for development
  getMockFirstBatchData() {
    return {
      sessionId: `mock-session-${Date.now()}`,
      firstBatch: [
        {
          employee: { name: "John Doe", id: 1 },
          department: { name: "Engineering", code: "ENG" },
          manager: { name: "Alice Smith", experience: 10 }
        },
        {
          employee: { name: "Jane Wilson", id: 2 },
          department: { name: "Marketing", code: "MKT" },
          manager: { name: "Bob Johnson", experience: 8 }
        }
      ],
      totalItems: 5,
      processedItems: 2,
      remainingItems: 3,
      hasMoreData: true,
      _metadata: {
        source: 'mock-data',
        endpoint: 'first-batch',
        timestamp: new Date().toISOString(),
        transformedBy: 'bff-nodejs'
      }
    };
  }

  getMockRemainingData(sessionId) {
    return {
      sessionId: sessionId,
      remainingData: [
        {
          employee: { name: "Charlie Brown", id: 3 },
          department: { name: "Sales", code: "SAL" },
          manager: { name: "Diana Prince", experience: 12 }
        },
        {
          employee: { name: "David Wilson", id: 4 },
          department: { name: "HR", code: "HR" },
          manager: { name: "Eva Green", experience: 15 }
        },
        {
          employee: { name: "Frank Miller", id: 5 },
          department: { name: "Finance", code: "FIN" },
          manager: { name: "Grace Lee", experience: 9 }
        }
      ],
      processedItems: 3,
      hasMoreData: false,
      message: "Mock remaining data processed successfully",
      _metadata: {
        source: 'mock-data',
        endpoint: 'remaining-data',
        timestamp: new Date().toISOString(),
        transformedBy: 'bff-nodejs'
      }
    };
  }

  getMockSessionStatus(sessionId) {
    return {
      sessionId: sessionId,
      exists: sessionId.includes('mock'),
      remainingCount: sessionId.includes('mock') ? 3 : 0,
      hasMoreData: sessionId.includes('mock'),
      _metadata: {
        source: 'mock-data',
        endpoint: 'session-status',
        timestamp: new Date().toISOString(),
        transformedBy: 'bff-nodejs'
      }
    };
  }

  getMockBatchControlledData() {
    return [
      {
        employee: { name: "John Doe", id: 1 },
        department: { name: "Engineering", code: "ENG" },
        manager: { name: "Alice Smith", experience: 10 },
        _bffMetadata: {
          processedAt: new Date().toISOString(),
          source: 'mock-batch-controlled',
          version: '1.0'
        }
      },
      {
        employee: { name: "Jane Wilson", id: 2 },
        department: { name: "Marketing", code: "MKT" },
        manager: { name: "Bob Johnson", experience: 8 },
        _bffMetadata: {
          processedAt: new Date().toISOString(),
          source: 'mock-batch-controlled',
          version: '1.0'
        }
      }
    ];
  }

  createBatchProcessingError(message, originalError) {
    const error = new Error(message);
    error.status = originalError?.response?.status || 500;
    error.originalError = originalError?.message;
    error.service = 'batch-processing';
    return error;
  }

  // Health check method
  async healthCheck() {
    try {
      const response = await this.batchClient.get('/actuator/health');
      return { 
        status: 'UP', 
        service: 'batch-processing-service', 
        details: response.data 
      };
    } catch (error) {
      return { 
        status: 'DOWN', 
        service: 'batch-processing-service', 
        error: error.message 
      };
    }
  }
}

module.exports = new BatchProcessingService();
