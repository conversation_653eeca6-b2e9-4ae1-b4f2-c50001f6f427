const axios = require('axios');

class SpringBootService {
  constructor() {
    this.employeeServiceUrl = process.env.EMPLOYEE_SERVICE_URL || 'http://localhost:8080/employee-department-manager';
    this.externalServiceUrl = process.env.EXTERNAL_SERVICE_URL || 'http://localhost:8081';
    this.timeout = parseInt(process.env.API_TIMEOUT) || 10000;
    this.retryAttempts = parseInt(process.env.RETRY_ATTEMPTS) || 3;
    
    // Create axios instances for different services
    this.employeeClient = axios.create({
      baseURL: this.employeeServiceUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.externalClient = axios.create({
      baseURL: this.externalServiceUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Add request/response interceptors for logging and error handling
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    const requestInterceptor = (config) => {
      console.log(`🔄 API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
      return config;
    };

    // Response interceptor
    const responseInterceptor = (response) => {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`);
      return response;
    };

    // Error interceptor
    const errorInterceptor = (error) => {
      console.error(`❌ API Error: ${error.config?.url}`, error.message);
      return Promise.reject(error);
    };

    [this.employeeClient, this.externalClient].forEach(client => {
      client.interceptors.request.use(requestInterceptor);
      client.interceptors.response.use(responseInterceptor, errorInterceptor);
    });
  }

  // Retry mechanism for failed requests
  async retryRequest(requestFn, attempts = this.retryAttempts) {
    try {
      return await requestFn();
    } catch (error) {
      if (attempts > 1 && this.isRetryableError(error)) {
        console.log(`🔄 Retrying request... ${attempts - 1} attempts left`);
        await this.delay(1000); // Wait 1 second before retry
        return this.retryRequest(requestFn, attempts - 1);
      }
      throw error;
    }
  }

  isRetryableError(error) {
    return error.code === 'ECONNRESET' || 
           error.code === 'ETIMEDOUT' || 
           (error.response && error.response.status >= 500);
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Employee Service Methods
  async getAllEmployeeData() {
    try {
      return this.retryRequest(async () => {
        const response = await this.employeeClient.get('/employee-data-list');
        return response.data;
      });
    } catch (error) {
      console.warn('⚠️ Spring Boot service unavailable, returning mock data for testing');
      // Return mock data when Spring Boot service is not available
      return [
        {
          employee: { employeeId: 101, name: "John Doe" },
          department: { name: "Engineering", code: "ENG-01" },
          manager: { name: "Alice Smith", experience: 10 }
        },
        {
          employee: { employeeId: 102, name: "Jane Wilson" },
          department: { name: "Marketing", code: "MKT-01" },
          manager: { name: "Bob Johnson", experience: 8 }
        },
        {
          employee: { employeeId: 103, name: "Mike Brown" },
          department: { name: "Sales", code: "SAL-01" },
          manager: { name: "Carol Davis", experience: 12 }
        }
      ];
    }
  }

  async getEmployeeDataById(employeeId) {
    try {
      return this.retryRequest(async () => {
        const response = await this.employeeClient.get(`/employee-data/${employeeId}`);
        return response.data;
      });
    } catch (error) {
      console.warn(`⚠️ Spring Boot service unavailable, returning mock data for employee ID: ${employeeId}`);
      // Return mock data when Spring Boot service is not available
      const mockData = [
        {
          employee: { employeeId: 101, name: "John Doe" },
          department: { name: "Engineering", code: "ENG-01" },
          manager: { name: "Alice Smith", experience: 10 }
        },
        {
          employee: { employeeId: 102, name: "Jane Wilson" },
          department: { name: "Marketing", code: "MKT-01" },
          manager: { name: "Bob Johnson", experience: 8 }
        },
        {
          employee: { employeeId: 103, name: "Mike Brown" },
          department: { name: "Sales", code: "SAL-01" },
          manager: { name: "Carol Davis", experience: 12 }
        }
      ];

      // Find the specific employee or return the first one
      const foundEmployee = mockData.find(item => item.employee.employeeId === parseInt(employeeId));
      return foundEmployee ? [foundEmployee] : [mockData[0]];
    }
  }

  async insertEmployeeData(payload) {
    return this.retryRequest(async () => {
      const response = await this.employeeClient.post('/insert', payload);
      return response.data;
    });
  }

  async deleteEmployeeData(employeeId) {
    return this.retryRequest(async () => {
      const response = await this.employeeClient.delete(`/delete/${employeeId}`);
      return response.data;
    });
  }

  // External Service Methods (for future microservices)
  async getExternalData(endpoint) {
    return this.retryRequest(async () => {
      const response = await this.externalClient.get(endpoint);
      return response.data;
    });
  }

  // Combined data aggregation methods
  async getCombinedEmployeeData() {
    try {
      // Get data from multiple sources and combine
      const [employeeData] = await Promise.all([
        this.getAllEmployeeData()
        // Add more service calls here as needed
        // this.getExternalData('/additional-data'),
        // this.getAnotherService('/other-endpoint')
      ]);

      // Transform and combine data as needed
      return this.transformEmployeeData(employeeData);
    } catch (error) {
      console.error('Error getting combined employee data:', error);
      throw error;
    }
  }

  async getCombinedEmployeeDataById(employeeId) {
    try {
      // Get data from multiple sources for specific employee
      const [employeeData] = await Promise.all([
        this.getEmployeeDataById(employeeId)
        // Add more service calls here as needed
      ]);

      return this.transformEmployeeData(employeeData);
    } catch (error) {
      console.error(`Error getting combined employee data for ID ${employeeId}:`, error);
      throw error;
    }
  }

  // Data transformation methods
  transformEmployeeData(data) {
    // Add any data transformation logic here
    // For example: formatting, enrichment, filtering, etc.
    if (Array.isArray(data)) {
      return data.map(item => this.enrichEmployeeRecord(item));
    } else {
      return this.enrichEmployeeRecord(data);
    }
  }

  enrichEmployeeRecord(record) {
    // Transform nested structure to flat structure expected by frontend
    const flatRecord = {
      // Employee data
      employeeId: record.employee?.employeeId || record.employeeId,
      empName: record.employee?.name || record.empName,

      // Department data
      deptName: record.department?.name || record.deptName,
      deptCode: record.department?.code || record.deptCode,

      // Manager data
      managerName: record.manager?.name || record.managerName,
      experience: record.manager?.experience || record.experience,

      // Keep any additional fields that might exist
      ...record,

      // Add metadata
      _metadata: {
        source: 'spring-boot-service',
        timestamp: new Date().toISOString(),
        version: '1.0',
        originalStructure: record.employee ? 'nested' : 'flat'
      }
    };

    // Remove nested objects to avoid confusion
    delete flatRecord.employee;
    delete flatRecord.department;
    delete flatRecord.manager;

    return flatRecord;
  }

  // Health check method
  async healthCheck() {
    try {
      const response = await this.employeeClient.get('/actuator/health');
      return { status: 'UP', service: 'employee-service', details: response.data };
    } catch (error) {
      return { status: 'DOWN', service: 'employee-service', error: error.message };
    }
  }
}

module.exports = new SpringBootService();
