#!/usr/bin/env node

const axios = require('axios');

const BFF_BASE_URL = 'http://localhost:4000';

console.log('🔐 Testing BFF with Authentication...\n');

let authToken = null;

async function loginUser() {
  try {
    console.log('1️⃣ Logging in as admin user...');
    const response = await axios.post(`${BFF_BASE_URL}/api/auth/login`, {
      identifier: 'admin',
      password: 'admin123'
    });
    
    authToken = response.data.data.tokens.accessToken;
    console.log('✅ Login successful');
    console.log(`   👤 User: ${response.data.data.user.username}`);
    console.log(`   🔑 Token: ${authToken.substring(0, 20)}...`);
    return true;
  } catch (error) {
    console.log('❌ Login failed:', error.response?.data?.error?.message || error.message);
    return false;
  }
}

async function testAuthenticatedEmployeeEndpoint() {
  try {
    console.log('2️⃣ Testing authenticated employee endpoint...');
    const response = await axios.get(`${BFF_BASE_URL}/api/employees`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    console.log('✅ Employee endpoint accessible with auth');
    console.log(`   📊 Retrieved ${response.data.count} records`);
    
    // Check data structure
    if (response.data.data && response.data.data.length > 0) {
      const firstRecord = response.data.data[0];
      console.log('\n📋 First record structure:');
      console.log('   - employeeId:', firstRecord.employeeId);
      console.log('   - empName:', firstRecord.empName);
      console.log('   - deptName:', firstRecord.deptName);
      console.log('   - deptCode:', firstRecord.deptCode);
      console.log('   - managerName:', firstRecord.managerName);
      console.log('   - experience:', firstRecord.experience);
      
      // Check if transformation worked
      const hasNestedStructure = firstRecord.employee || firstRecord.department || firstRecord.manager;
      if (hasNestedStructure) {
        console.log('⚠️ WARNING: Data still has nested structure!');
        console.log('   This means the transformation is not working properly.');
      } else {
        console.log('✅ Data transformation successful - flat structure confirmed');
      }
    }
    
    return true;
  } catch (error) {
    console.log('❌ Authenticated employee endpoint failed:', error.response?.data?.error?.message || error.message);
    return false;
  }
}

async function testEmployeeById() {
  try {
    console.log('3️⃣ Testing employee by ID endpoint...');
    const response = await axios.get(`${BFF_BASE_URL}/api/employees/101`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    console.log('✅ Employee by ID endpoint working');
    
    if (response.data.data && response.data.data.length > 0) {
      const record = response.data.data[0];
      console.log('   📋 Employee 101 data:');
      console.log(`      Name: ${record.empName}`);
      console.log(`      Department: ${record.deptName}`);
      console.log(`      Manager: ${record.managerName}`);
    }
    
    return true;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('⚠️ Employee 101 not found (this is OK for testing)');
      return true;
    }
    console.log('❌ Employee by ID endpoint failed:', error.response?.data?.error?.message || error.message);
    return false;
  }
}

async function testUnauthenticatedAccess() {
  try {
    console.log('4️⃣ Testing unauthenticated access (should fail)...');
    await axios.get(`${BFF_BASE_URL}/api/employees`);
    console.log('❌ SECURITY ISSUE: Unauthenticated access succeeded!');
    return false;
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Unauthenticated access properly blocked');
      return true;
    }
    console.log('❌ Unexpected error:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Authenticated BFF Tests\n');
  
  const tests = [
    loginUser,
    testAuthenticatedEmployeeEndpoint,
    testEmployeeById,
    testUnauthenticatedAccess
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    const result = await test();
    if (result) {
      passed++;
    } else {
      failed++;
    }
    console.log(''); // Add spacing
  }

  console.log('📊 Test Results:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%\n`);

  if (failed === 0) {
    console.log('🎉 All tests passed! Authentication and data transformation working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the issues above.');
  }

  process.exit(failed > 0 ? 1 : 0);
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Test interrupted by user');
  process.exit(1);
});

// Run the tests
runTests().catch(error => {
  console.error('💥 Unexpected error during testing:', error);
  process.exit(1);
});
