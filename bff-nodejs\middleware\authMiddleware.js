const jwtService = require('../services/jwtService');
const userService = require('../services/userService');

/**
 * JWT Authentication Middleware
 * Validates JWT tokens and adds user info to request object
 */
const authenticateToken = (options = {}) => {
  return async (req, res, next) => {
    try {
      // Extract token from Authorization header
      const authHeader = req.headers.authorization;
      const token = jwtService.extractTokenFromHeader(authHeader);

      if (!token) {
        return res.status(401).json({
          error: {
            message: 'Access token is required',
            status: 401,
            code: 'TOKEN_MISSING',
            timestamp: new Date().toISOString()
          }
        });
      }

      // Verify token
      const decoded = jwtService.verifyToken(token);

      // Check if it's an access token
      if (decoded.type !== 'access') {
        return res.status(401).json({
          error: {
            message: 'Invalid token type',
            status: 401,
            code: 'INVALID_TOKEN_TYPE',
            timestamp: new Date().toISOString()
          }
        });
      }

      // Get user details
      const user = userService.getUserById(decoded.userId);
      if (!user) {
        return res.status(401).json({
          error: {
            message: 'User not found',
            status: 401,
            code: 'USER_NOT_FOUND',
            timestamp: new Date().toISOString()
          }
        });
      }

      if (!user.isActive) {
        return res.status(401).json({
          error: {
            message: 'Account is deactivated',
            status: 401,
            code: 'ACCOUNT_DEACTIVATED',
            timestamp: new Date().toISOString()
          }
        });
      }

      // Add user info to request
      req.user = user;
      req.token = token;
      req.tokenPayload = decoded;

      console.log(`🔐 Authenticated user: ${user.username} (${user.role})`);
      next();

    } catch (error) {
      console.error('❌ Authentication error:', error.message);

      let errorCode = 'TOKEN_INVALID';
      let statusCode = 401;

      if (error.message === 'Token has expired') {
        errorCode = 'TOKEN_EXPIRED';
      } else if (error.message === 'Invalid token') {
        errorCode = 'TOKEN_INVALID';
      }

      return res.status(statusCode).json({
        error: {
          message: error.message,
          status: statusCode,
          code: errorCode,
          timestamp: new Date().toISOString()
        }
      });
    }
  };
};

/**
 * Role-based Authorization Middleware
 * Checks if user has required role(s)
 */
const requireRole = (roles) => {
  // Ensure roles is an array
  const requiredRoles = Array.isArray(roles) ? roles : [roles];

  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: {
            message: 'Authentication required',
            status: 401,
            code: 'AUTH_REQUIRED',
            timestamp: new Date().toISOString()
          }
        });
      }

      const userRole = req.user.role;
      const hasRequiredRole = requiredRoles.includes(userRole) || userRole === 'admin';

      if (!hasRequiredRole) {
        console.log(`🚫 Access denied for user ${req.user.username}: required roles [${requiredRoles.join(', ')}], user role: ${userRole}`);
        
        return res.status(403).json({
          error: {
            message: 'Insufficient permissions',
            status: 403,
            code: 'INSUFFICIENT_PERMISSIONS',
            requiredRoles,
            userRole,
            timestamp: new Date().toISOString()
          }
        });
      }

      console.log(`✅ Role check passed for user ${req.user.username}: ${userRole}`);
      next();

    } catch (error) {
      console.error('❌ Authorization error:', error);
      return res.status(500).json({
        error: {
          message: 'Authorization check failed',
          status: 500,
          code: 'AUTH_CHECK_FAILED',
          timestamp: new Date().toISOString()
        }
      });
    }
  };
};

/**
 * Optional Authentication Middleware
 * Adds user info if token is present, but doesn't require it
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = jwtService.extractTokenFromHeader(authHeader);

    if (token) {
      try {
        const decoded = jwtService.verifyToken(token);
        if (decoded.type === 'access') {
          const user = userService.getUserById(decoded.userId);
          if (user && user.isActive) {
            req.user = user;
            req.token = token;
            req.tokenPayload = decoded;
            console.log(`🔐 Optional auth: user ${user.username} authenticated`);
          }
        }
      } catch (error) {
        // Ignore token errors for optional auth
        console.log(`⚠️ Optional auth: invalid token ignored`);
      }
    }

    next();
  } catch (error) {
    console.error('❌ Optional auth error:', error);
    next(); // Continue without authentication
  }
};

/**
 * Admin Only Middleware
 * Shorthand for requiring admin role
 */
const adminOnly = [authenticateToken(), requireRole(['admin'])];

/**
 * User or Admin Middleware
 * Allows both user and admin roles
 */
const userOrAdmin = [authenticateToken(), requireRole(['user', 'admin'])];

/**
 * Self or Admin Middleware
 * Allows users to access their own data or admins to access any data
 */
const selfOrAdmin = (userIdParam = 'userId') => {
  return [
    authenticateToken(),
    (req, res, next) => {
      const requestedUserId = req.params[userIdParam];
      const currentUserId = req.user.userId;
      const isAdmin = req.user.role === 'admin';

      if (isAdmin || currentUserId === requestedUserId) {
        next();
      } else {
        return res.status(403).json({
          error: {
            message: 'Can only access your own data',
            status: 403,
            code: 'ACCESS_DENIED',
            timestamp: new Date().toISOString()
          }
        });
      }
    }
  ];
};

/**
 * Rate Limiting Middleware for Authentication Routes
 */
const authRateLimit = require('express-rate-limit')({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs for auth routes
  message: {
    error: {
      message: 'Too many authentication attempts, please try again later',
      status: 429,
      code: 'RATE_LIMIT_EXCEEDED',
      timestamp: new Date().toISOString()
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    console.log(`🚫 Rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      error: {
        message: 'Too many authentication attempts, please try again later',
        status: 429,
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.round(req.rateLimit.resetTime / 1000),
        timestamp: new Date().toISOString()
      }
    });
  }
});

module.exports = {
  authenticateToken,
  requireRole,
  optionalAuth,
  adminOnly,
  userOrAdmin,
  selfOrAdmin,
  authRateLimit
};
