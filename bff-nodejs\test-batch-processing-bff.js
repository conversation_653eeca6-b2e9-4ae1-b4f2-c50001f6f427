const axios = require('axios');

// Configuration
const BFF_BASE_URL = 'http://localhost:4000';
const BATCH_API_URL = `${BFF_BASE_URL}/api/batch-processing`;

// Test credentials (adjust based on your auth setup)
const TEST_USER = {
  username: 'admin',
  password: 'admin123'
};

class BatchProcessingBFFTester {
  constructor() {
    this.authToken = null;
    this.sessionId = null;
    this.client = axios.create({
      baseURL: BFF_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  // Helper method to add auth headers
  getAuthHeaders() {
    return this.authToken ? {
      'Authorization': `Bearer ${this.authToken}`,
      'x-request-id': `test-${Date.now()}`
    } : {
      'x-request-id': `test-${Date.now()}`
    };
  }

  // Step 1: Authenticate with <PERSON><PERSON>
  async authenticate() {
    try {
      console.log('🔐 Step 1: Authenticating with <PERSON>FF...');
      
      const response = await this.client.post('/api/auth/login', TEST_USER);
      
      if (response.data.token) {
        this.authToken = response.data.token;
        console.log('✅ Authentication successful');
        console.log(`   Token: ${this.authToken.substring(0, 20)}...`);
        return true;
      } else {
        console.log('❌ Authentication failed - no token received');
        return false;
      }
    } catch (error) {
      console.log('❌ Authentication failed:', error.response?.data?.error?.message || error.message);
      console.log('⚠️  Proceeding without authentication for testing...');
      return false; // Continue testing even without auth for development
    }
  }

  // Step 2: Get BFF API information
  async getBffInfo() {
    try {
      console.log('\n📚 Step 2: Getting BFF API information...');
      
      const response = await this.client.get('/api/batch-processing/info', {
        headers: this.getAuthHeaders()
      });
      
      console.log('✅ BFF API Info retrieved:');
      console.log(`   Service: ${response.data.service}`);
      console.log(`   Version: ${response.data.version}`);
      console.log(`   Endpoints: ${response.data.endpoints.length} available`);
      
      return response.data;
    } catch (error) {
      console.log('❌ Failed to get BFF info:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  // Step 3: Check BFF health
  async checkBffHealth() {
    try {
      console.log('\n🏥 Step 3: Checking BFF health...');
      
      const response = await this.client.get('/api/batch-processing/health', {
        headers: this.getAuthHeaders()
      });
      
      console.log('✅ BFF Health check:');
      console.log(`   BFF Status: ${response.data.bffHealth?.status}`);
      console.log(`   Backend Status: ${response.data.status}`);
      console.log(`   Service: ${response.data.service}`);
      
      return response.data;
    } catch (error) {
      console.log('❌ Health check failed:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  // Step 4: Get first batch
  async getFirstBatch() {
    try {
      console.log('\n📦 Step 4: Getting first batch via BFF...');
      
      const response = await this.client.get('/api/batch-processing/first-batch', {
        headers: this.getAuthHeaders()
      });
      
      const data = response.data;
      this.sessionId = data.sessionId;
      
      console.log('✅ First batch received:');
      console.log(`   Session ID: ${data.sessionId}`);
      console.log(`   First Batch Items: ${data.firstBatch?.length || 0}`);
      console.log(`   Total Items: ${data.totalItems}`);
      console.log(`   Processed Items: ${data.processedItems}`);
      console.log(`   Remaining Items: ${data.remainingItems}`);
      console.log(`   Has More Data: ${data.hasMoreData}`);
      console.log(`   BFF Processing Time: ${data._bffInfo?.processingTimeMs}ms`);
      console.log(`   Request ID: ${data._bffInfo?.requestId}`);
      
      return data;
    } catch (error) {
      console.log('❌ Failed to get first batch:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  // Step 5: Check session status
  async checkSessionStatus() {
    try {
      if (!this.sessionId) {
        console.log('⚠️  No session ID available, skipping session status check');
        return null;
      }
      
      console.log('\n🔍 Step 5: Checking session status via BFF...');
      
      const response = await this.client.get(`/api/batch-processing/session-status?sessionId=${this.sessionId}`, {
        headers: this.getAuthHeaders()
      });
      
      const data = response.data;
      
      console.log('✅ Session status checked:');
      console.log(`   Session ID: ${data.sessionId}`);
      console.log(`   Exists: ${data.exists}`);
      console.log(`   Remaining Count: ${data.remainingCount}`);
      console.log(`   Has More Data: ${data.hasMoreData}`);
      console.log(`   BFF Processing Time: ${data._bffInfo?.processingTimeMs}ms`);
      
      return data;
    } catch (error) {
      console.log('❌ Failed to check session status:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  // Step 6: Get remaining data
  async getRemainingData() {
    try {
      if (!this.sessionId) {
        console.log('⚠️  No session ID available, skipping remaining data request');
        return null;
      }
      
      console.log('\n📥 Step 6: Getting remaining data via BFF...');
      
      const response = await this.client.get(`/api/batch-processing/remaining-data?sessionId=${this.sessionId}`, {
        headers: this.getAuthHeaders()
      });
      
      const data = response.data;
      
      console.log('✅ Remaining data received:');
      console.log(`   Session ID: ${data.sessionId}`);
      console.log(`   Remaining Data Items: ${data.remainingData?.length || 0}`);
      console.log(`   Processed Items: ${data.processedItems}`);
      console.log(`   Has More Data: ${data.hasMoreData}`);
      console.log(`   Message: ${data.message}`);
      console.log(`   BFF Processing Time: ${data._bffInfo?.processingTimeMs}ms`);
      
      return data;
    } catch (error) {
      console.log('❌ Failed to get remaining data:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  // Step 7: Test batch controlled endpoint
  async testBatchControlled() {
    try {
      console.log('\n⚡ Step 7: Testing batch controlled endpoint via BFF...');
      
      const response = await this.client.get('/api/batch-processing/batch-controlled', {
        headers: this.getAuthHeaders()
      });
      
      const data = response.data;
      
      console.log('✅ Batch controlled data received:');
      console.log(`   Data Items: ${data.data?.length || 0}`);
      console.log(`   BFF Processing Time: ${data._bffInfo?.processingTimeMs}ms`);
      console.log(`   Item Count: ${data._bffInfo?.itemCount}`);
      
      return data;
    } catch (error) {
      console.log('❌ Failed to get batch controlled data:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  // Step 8: Test updated employee data endpoint
  async testUpdatedEmployeeData() {
    try {
      console.log('\n🔧 Step 8: Testing updated employee data endpoint via BFF...');
      
      const response = await this.client.get('/api/batch-processing/updated-employee-data', {
        headers: this.getAuthHeaders()
      });
      
      const data = response.data;
      
      console.log('✅ Updated employee data received:');
      console.log(`   Data Items: ${data.data?.length || 0}`);
      console.log(`   BFF Processing Time: ${data._bffInfo?.processingTimeMs}ms`);
      console.log(`   Item Count: ${data._bffInfo?.itemCount}`);
      
      return data;
    } catch (error) {
      console.log('❌ Failed to get updated employee data:', error.response?.data?.error?.message || error.message);
      return null;
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🚀 Starting BFF Batch Processing Tests');
    console.log('=====================================\n');
    
    const startTime = Date.now();
    
    // Run tests in sequence
    await this.authenticate();
    await this.getBffInfo();
    await this.checkBffHealth();
    await this.getFirstBatch();
    await this.checkSessionStatus();
    await this.getRemainingData();
    await this.testBatchControlled();
    await this.testUpdatedEmployeeData();
    
    const totalTime = Date.now() - startTime;
    
    console.log('\n=====================================');
    console.log('🎉 BFF Batch Processing Tests Completed');
    console.log(`⏱️  Total execution time: ${totalTime}ms`);
    console.log('=====================================');
  }

  // Test error handling
  async testErrorHandling() {
    console.log('\n🧪 Testing error handling...');
    
    try {
      // Test with invalid session ID
      await this.client.get('/api/batch-processing/session-status?sessionId=invalid-session', {
        headers: this.getAuthHeaders()
      });
    } catch (error) {
      console.log('✅ Error handling test passed:', error.response?.data?.error?.message);
    }
  }
}

// Run the tests
async function main() {
  const tester = new BatchProcessingBFFTester();
  
  try {
    await tester.runAllTests();
    await tester.testErrorHandling();
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
  }
}

// Execute if run directly
if (require.main === module) {
  main();
}

module.exports = BatchProcessingBFFTester;
