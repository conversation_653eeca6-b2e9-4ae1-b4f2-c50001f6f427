# Required Spring Boot Backend Changes

Based on the updated BFF implementation with authentication, here are the changes required in your Spring Boot backend:

## 🚨 IMPORTANT: Authentication Changes

The BFF now includes **JWT-based authentication** which means:
- All employee endpoints now require authentication
- The BFF handles user authentication internally (no backend changes needed for auth)
- Employee endpoints are protected and require valid JWT tokens

## Key Changes Summary:
1. **No authentication backend needed** - BFF handles auth internally
2. **Employee endpoints remain the same** - but now called by authenticated BFF
3. **CORS updates required** - to allow BFF server access

## 1. Update CORS Configuration

**Current CORS Config:**
```java
@Configuration
public class CorsConfig {
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowCredentials(true);
        corsConfig.addAllowedOrigin("http://localhost:3000");
        corsConfig.addAllowedHeader("*");
        corsConfig.addAllowedMethod("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        return new CorsWebFilter(source);
    }
}
```

**Updated CORS Config (Required):**
```java
@Configuration
public class CorsConfig {
    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();
        corsConfig.setAllowCredentials(true);
        
        // Allow both React frontend and BFF server
        corsConfig.addAllowedOrigin("http://localhost:3000"); // React frontend
        corsConfig.addAllowedOrigin("http://localhost:4000"); // BFF server
        
        corsConfig.addAllowedHeader("*");
        corsConfig.addAllowedMethod("*");

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        return new CorsWebFilter(source);
    }
}
```

## 2. Required API Endpoints

Your Spring Boot backend must have these endpoints available:

### Base URL: `http://localhost:8080/employee-department-manager`

1. **GET `/employee-data-list`** - Get all employee data
   - Used by BFF endpoint: `GET /api/employees`
   - Must return array of employee objects

2. **GET `/employee-data/{id}`** - Get employee by ID
   - Used by BFF endpoint: `GET /api/employees/{id}`
   - Must return single employee object or 404

3. **POST `/insert`** - Insert new employee
   - Used by BFF endpoint: `POST /api/employees`
   - Expected payload format:
   ```json
   {
     "deptName": "string",
     "deptCode": "string",
     "empName": "string",
     "employeeId": "number",
     "managerName": "string",
     "experience": "string"
   }
   ```

4. **DELETE `/delete/{id}`** - Delete employee by ID
   - Used by BFF endpoint: `DELETE /api/employees/{id}`
   - Expected response format:
   ```json
   {
     "status": "success",
     "message": "Employee with ID {id} and related records deleted successfully"
   }
   ```

5. **GET `/actuator/health`** (Optional but recommended)
   - Used for health checks by BFF
   - Standard Spring Boot Actuator endpoint

## 3. Expected Response Format

Your endpoints should return data in this format:

### Employee Object Structure:
```json
{
  "employeeId": 101,
  "empName": "John Doe",
  "deptName": "Engineering",
  "deptCode": "ENG",
  "managerName": "Jane Smith",
  "experience": "5 years"
}
```

### List Response:
```json
[
  {
    "employeeId": 101,
    "empName": "John Doe",
    "deptName": "Engineering",
    "deptCode": "ENG", 
    "managerName": "Jane Smith",
    "experience": "5 years"
  },
  // ... more employees
]
```

## 4. Error Handling

Ensure your endpoints return appropriate HTTP status codes:
- 200: Success
- 404: Employee not found (for GET by ID)
- 409: Conflict (for POST when employee already exists)
- 400: Bad request (for invalid data)
- 500: Internal server error

## 5. Content-Type Headers

Make sure your endpoints return:
```
Content-Type: application/json
```

## 6. Testing the Integration

After making these changes, you can test the integration using:

```bash
# From the bff-nodejs directory
node test-bff.js
```

This will test:
1. BFF health check
2. Direct Spring Boot connection
3. BFF employee endpoints
4. BFF employee by ID endpoint

## 7. Port Configuration

Ensure your Spring Boot application runs on port 8080 (default) or update the BFF configuration in `.env` file if using a different port.

## 8. Authentication Flow (No Backend Changes Required)

The BFF now handles authentication completely:

### Authentication Endpoints (BFF Internal):
- `POST /api/auth/login` - User login (handled by BFF)
- `POST /api/auth/register` - User registration (admin only, handled by BFF)
- `POST /api/auth/refresh` - Token refresh (handled by BFF)
- `POST /api/auth/logout` - User logout (handled by BFF)

### Protected Employee Endpoints:
- All employee endpoints now require `Authorization: Bearer <token>` header
- BFF validates JWT tokens before calling Spring Boot backend
- Spring Boot backend receives requests only from authenticated BFF

### Default Users (for testing):
- **Admin**: username=`admin`, password=`admin123`
- **User**: username=`user`, password=`user123`

## Summary of Changes Needed:

1. ✅ **Add BFF server to CORS allowed origins** (`http://localhost:4000`)
2. ✅ **Ensure all required endpoints exist** with correct paths
3. ✅ **Verify response format** matches expected structure
4. ✅ **Test error handling** for various scenarios
5. ✅ **Enable Spring Boot Actuator** for health checks (optional)
6. 🆕 **No authentication changes needed** - BFF handles all auth internally
