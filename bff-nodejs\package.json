{"name": "employee-bff", "version": "1.0.0", "description": "Node.js BFF for Employee Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.4.0", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^7.1.5", "joi": "^17.11.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["bff", "backend-for-frontend", "express", "microservices"], "author": "", "license": "ISC"}