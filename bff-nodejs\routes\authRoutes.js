const express = require('express');
const Joi = require('joi');
const router = express.Router();
const userService = require('../services/userService');
const jwtService = require('../services/jwtService');
const { authenticateToken, authRateLimit, adminOnly } = require('../middleware/authMiddleware');

// Validation schemas
const loginSchema = Joi.object({
  identifier: Joi.string().min(3).required().messages({
    'string.min': 'Username/email must be at least 3 characters long',
    'any.required': 'Username or email is required'
  }),
  password: Joi.string().min(6).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'any.required': 'Password is required'
  })
});

const registerSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).required().messages({
    'string.alphanum': 'Username must contain only alphanumeric characters',
    'string.min': 'Username must be at least 3 characters long',
    'string.max': 'Username must not exceed 30 characters',
    'any.required': 'Username is required'
  }),
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required'
  }),
  password: Joi.string().min(6).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])')).required().messages({
    'string.min': 'Password must be at least 6 characters long',
    'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, and one number',
    'any.required': 'Password is required'
  }),
  role: Joi.string().valid('user', 'admin').default('user')
});

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required().messages({
    'any.required': 'Refresh token is required'
  })
});

// Middleware for request logging
router.use((req, res, next) => {
  console.log(`🔐 Auth Request: ${req.method} ${req.originalUrl} from ${req.ip}`);
  next();
});

// POST /api/auth/login - User login
router.post('/login', authRateLimit, async (req, res, next) => {
  try {
    console.log('🔑 Login attempt for:', req.body.identifier);

    // Validate request body
    const { error, value } = loginSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: error.details.map(detail => detail.message),
          status: 400,
          code: 'VALIDATION_ERROR',
          timestamp: new Date().toISOString()
        }
      });
    }

    const { identifier, password } = value;

    // Authenticate user
    const result = await userService.authenticateUser(identifier, password);

    console.log(`✅ Login successful for user: ${result.user.username}`);

    res.json({
      success: true,
      message: result.message,
      data: {
        user: result.user,
        tokens: result.tokens
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Login error:', error.message);
    
    // Don't expose internal error details for security
    const statusCode = error.message.includes('Invalid credentials') ? 401 : 500;
    const message = error.message.includes('Invalid credentials') || error.message.includes('Account is deactivated') 
      ? error.message 
      : 'Login failed';

    next({
      status: statusCode,
      message: message,
      code: statusCode === 401 ? 'INVALID_CREDENTIALS' : 'LOGIN_FAILED'
    });
  }
});

// POST /api/auth/register - User registration (admin only for now)
router.post('/register', adminOnly, async (req, res, next) => {
  try {
    console.log('📝 Registration attempt for:', req.body.username);

    // Validate request body
    const { error, value } = registerSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: error.details.map(detail => detail.message),
          status: 400,
          code: 'VALIDATION_ERROR',
          timestamp: new Date().toISOString()
        }
      });
    }

    // Create user
    const newUser = await userService.createUser(value);

    console.log(`✅ User registered successfully: ${newUser.username}`);

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: newUser
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Registration error:', error.message);
    
    const statusCode = error.message.includes('already exists') ? 409 : 500;
    
    next({
      status: statusCode,
      message: error.message,
      code: statusCode === 409 ? 'USER_EXISTS' : 'REGISTRATION_FAILED'
    });
  }
});

// POST /api/auth/refresh - Refresh access token
router.post('/refresh', async (req, res, next) => {
  try {
    console.log('🔄 Token refresh attempt');

    // Validate request body
    const { error, value } = refreshTokenSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: {
          message: 'Validation failed',
          details: error.details.map(detail => detail.message),
          status: 400,
          code: 'VALIDATION_ERROR',
          timestamp: new Date().toISOString()
        }
      });
    }

    const { refreshToken } = value;

    // Refresh token
    const result = await userService.refreshToken(refreshToken);

    console.log('✅ Token refreshed successfully');

    res.json({
      success: true,
      message: result.message,
      data: {
        tokens: result.tokens
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Token refresh error:', error.message);
    
    next({
      status: 401,
      message: error.message,
      code: 'TOKEN_REFRESH_FAILED'
    });
  }
});

// POST /api/auth/logout - User logout
router.post('/logout', authenticateToken(), async (req, res, next) => {
  try {
    console.log(`🚪 Logout attempt for user: ${req.user.username}`);

    const refreshToken = req.body.refreshToken;
    
    if (refreshToken) {
      await userService.logoutUser(refreshToken);
    }

    console.log(`✅ Logout successful for user: ${req.user.username}`);

    res.json({
      success: true,
      message: 'Logout successful',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Logout error:', error.message);
    
    next({
      status: 500,
      message: 'Logout failed',
      code: 'LOGOUT_FAILED'
    });
  }
});

// GET /api/auth/me - Get current user info
router.get('/me', authenticateToken(), async (req, res) => {
  try {
    console.log(`👤 Profile request for user: ${req.user.username}`);

    res.json({
      success: true,
      data: {
        user: req.user,
        tokenInfo: {
          issuedAt: new Date(req.tokenPayload.iat * 1000),
          expiresAt: new Date(req.tokenPayload.exp * 1000)
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Profile request error:', error);
    res.status(500).json({
      error: {
        message: 'Failed to get user profile',
        status: 500,
        code: 'PROFILE_FAILED',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// GET /api/auth/verify - Verify token validity
router.get('/verify', authenticateToken(), async (req, res) => {
  try {
    const tokenExpiration = jwtService.getTokenExpiration(req.token);
    const timeUntilExpiry = tokenExpiration ? Math.floor((tokenExpiration.getTime() - Date.now()) / 1000) : null;

    res.json({
      success: true,
      data: {
        valid: true,
        user: req.user,
        expiresAt: tokenExpiration,
        expiresInSeconds: timeUntilExpiry
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Token verification error:', error);
    res.status(500).json({
      error: {
        message: 'Token verification failed',
        status: 500,
        code: 'VERIFICATION_FAILED',
        timestamp: new Date().toISOString()
      }
    });
  }
});

// GET /api/auth/users - Get all users (admin only)
router.get('/users', adminOnly, async (req, res, next) => {
  try {
    console.log(`👥 Users list request by admin: ${req.user.username}`);

    const users = userService.getAllUsers();

    res.json({
      success: true,
      data: {
        users,
        count: users.length
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Users list error:', error);
    next({
      status: 500,
      message: 'Failed to get users list',
      code: 'USERS_LIST_FAILED'
    });
  }
});

module.exports = router;
