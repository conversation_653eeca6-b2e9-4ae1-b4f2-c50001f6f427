#!/usr/bin/env node

const axios = require('axios');

const BFF_BASE_URL = 'http://localhost:4000';

// Expected frontend data structure
const EXPECTED_FIELDS = [
  'employeeId',
  'empName', 
  'deptName',
  'deptCode',
  'managerName',
  'experience'
];

async function validateDataFormat() {
  console.log('🔍 Validating Node.js API Data Format for Frontend...\n');

  try {
    // Step 1: Authenticate
    console.log('🔐 Step 1: Authenticating...');
    const loginResponse = await axios.post(`${BFF_BASE_URL}/api/auth/login`, {
      identifier: 'admin',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.tokens.accessToken;
    console.log('✅ Authentication successful\n');

    // Step 2: Fetch employee data
    console.log('📊 Step 2: Fetching employee data...');
    const response = await axios.get(`${BFF_BASE_URL}/api/employees`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Data fetched successfully\n');

    // Step 3: Validate response structure
    console.log('🔍 Step 3: Validating response structure...');
    
    const responseData = response.data;
    
    // Check top-level response structure
    console.log('📋 Response Structure Validation:');
    console.log(`   ✅ Has 'success' field: ${responseData.hasOwnProperty('success')}`);
    console.log(`   ✅ Has 'data' field: ${responseData.hasOwnProperty('data')}`);
    console.log(`   ✅ Has 'count' field: ${responseData.hasOwnProperty('count')}`);
    console.log(`   ✅ Success is true: ${responseData.success === true}`);
    console.log(`   ✅ Data is array: ${Array.isArray(responseData.data)}`);
    console.log(`   ✅ Count matches: ${responseData.count === responseData.data.length}\n`);

    const employees = responseData.data;

    if (!employees || employees.length === 0) {
      console.log('❌ No employee data found');
      return false;
    }

    // Step 4: Validate each employee record
    console.log('🧪 Step 4: Validating employee records...\n');
    
    let allValid = true;
    
    employees.forEach((employee, index) => {
      console.log(`📝 Record ${index + 1} Validation:`);
      
      // Check required fields
      const missingFields = EXPECTED_FIELDS.filter(field => !employee.hasOwnProperty(field));
      const extraNestedFields = ['employee', 'department', 'manager'].filter(field => employee.hasOwnProperty(field));
      
      if (missingFields.length === 0) {
        console.log(`   ✅ All required fields present`);
      } else {
        console.log(`   ❌ Missing fields: ${missingFields.join(', ')}`);
        allValid = false;
      }
      
      if (extraNestedFields.length === 0) {
        console.log(`   ✅ No nested objects (flat structure)`);
      } else {
        console.log(`   ⚠️  Still has nested objects: ${extraNestedFields.join(', ')}`);
        allValid = false;
      }
      
      // Validate data types
      console.log(`   📊 Data Types:`);
      console.log(`      employeeId: ${typeof employee.employeeId} (${employee.employeeId})`);
      console.log(`      empName: ${typeof employee.empName} ("${employee.empName}")`);
      console.log(`      deptName: ${typeof employee.deptName} ("${employee.deptName}")`);
      console.log(`      deptCode: ${typeof employee.deptCode} ("${employee.deptCode}")`);
      console.log(`      managerName: ${typeof employee.managerName} ("${employee.managerName}")`);
      console.log(`      experience: ${typeof employee.experience} (${employee.experience})`);
      
      // Check for null/undefined values
      const nullFields = EXPECTED_FIELDS.filter(field => 
        employee[field] === null || employee[field] === undefined || employee[field] === ''
      );
      
      if (nullFields.length === 0) {
        console.log(`   ✅ No null/empty values`);
      } else {
        console.log(`   ⚠️  Null/empty fields: ${nullFields.join(', ')}`);
      }
      
      console.log('');
    });

    // Step 5: Frontend compatibility test
    console.log('🎯 Step 5: Frontend Compatibility Test...\n');
    
    console.log('📋 Sample Frontend Code Test:');
    console.log('```javascript');
    console.log('// This is how your React component would access the data:');
    console.log('const response = await fetch("/api/employees", { headers: { Authorization: "Bearer " + token } });');
    console.log('const result = await response.json();');
    console.log('const employees = result.data;');
    console.log('');
    console.log('employees.map(employee => (');
    console.log('  <div key={employee.employeeId}>');
    console.log('    <h3>{employee.empName}</h3>');
    console.log('    <p>Department: {employee.deptName} ({employee.deptCode})</p>');
    console.log('    <p>Manager: {employee.managerName}</p>');
    console.log('    <p>Experience: {employee.experience} years</p>');
    console.log('  </div>');
    console.log('));');
    console.log('```\n');

    // Step 6: Show actual data sample
    console.log('📊 Step 6: Actual Data Sample (First Record):');
    const firstEmployee = employees[0];
    console.log(JSON.stringify({
      employeeId: firstEmployee.employeeId,
      empName: firstEmployee.empName,
      deptName: firstEmployee.deptName,
      deptCode: firstEmployee.deptCode,
      managerName: firstEmployee.managerName,
      experience: firstEmployee.experience
    }, null, 2));

    // Final validation result
    console.log('\n🏆 Final Validation Result:');
    if (allValid) {
      console.log('✅ SUCCESS: Data format is perfect for frontend consumption!');
      console.log('✅ All required fields present');
      console.log('✅ Flat structure (no nested objects)');
      console.log('✅ Proper data types');
      console.log('✅ Ready for React components');
    } else {
      console.log('❌ ISSUES FOUND: Data format needs adjustment');
      console.log('   Check the validation details above');
    }

    return allValid;

  } catch (error) {
    console.error('❌ Validation failed:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.log('\n🔐 Authentication Error:');
      console.log('   Make sure the BFF server is running on port 4000');
      console.log('   Default credentials: admin/admin123');
    }
    
    return false;
  }
}

// Run validation
validateDataFormat().then(success => {
  process.exit(success ? 0 : 1);
});
